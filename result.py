from jsonpath import jsonpath
import csv

from request import get_result, get_model

# model = "全息直播C端服务"
c_urls= [
    "http://imkf.mulan.corpautohome.com",
    "http://ahohconsumerdev.mulan.corpautohome.com",
    "http://marketing-api.dp.terra.corpautohome.com/"
]
live = ["http://live.mulan.corpautohome.com"]
base_url =
model_url = f"{base_url}/api/swagger-resources"
result_url = f"{base_url}/api/v3/api-docs?"


i=0
for model in get_model():
    rq_json = get_result(model)
    a_list =  jsonpath(rq_json, "$.paths")
    if isinstance(a_list, list):
        path_list =a_list[0]

        for key, value in path_list.items():
            info = ["/api"+key]
            for key_1, value_1 in value.items():
                info.append(key_1)
                info.append(model)
                info.append(value_1.get("tags")[0])
                info.insert(0,value_1.get("summary"))

            print("\t".join(info))


